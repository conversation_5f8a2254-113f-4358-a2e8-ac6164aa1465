"""整个量化程序的入口"""
# 根据需要往tasks中添加monitor或者trader即可。
# 也可以单独执行某个或某些monitor或者trader

import asyncio
import os
import traceback
from dotenv import load_dotenv
import re

from utils.utils_logger import setup_logger
from constants import TASK_NAME_PREFIX
from utils.utils_telegram import send_telegram_message_without_prefix, wait_for_message_queue, set_program_start_time, set_telegram_logger
from utils.config_manager import get_app_config
from utils.utils_process_lock import ensure_single_instance
from utils.utils_logo import show_startup_banner, get_simple_logo

from utils.utils_common import print_ctrl_c_newline
from utils.resource_monitor import print_resource_report, get_basic_memory_info, periodic_memory_monitor
from utils.exchange_manager import close_global_exchange
from utils.signal_handler import setup_signal_handlers, wait_for_stop_signal, get_signal_handler
from utils.telegram_bot_manager import run_telegram_bot

# 延迟导入monitor和trader模块，减少初始内存占用
# from monitor import monitor1_volume,monitor2_pressure,monitor3_divergence,monitor4_oi,monitor5_ema_ma_crossover,monitor6_volume_oi_matrix,monitor7_bollinger_squeeze
# from trader import trader1_ema,trader3_orderbook

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)

# 加载.env文件, 读取bot_token和chat_id 【已经添加.env到.gitignore中，所以不会被提交到git中，也就不会泄露】
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN_QUANT")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

task_results = []  # 用于存储任务结果
telegram_bot_task_name = f"{TASK_NAME_PREFIX}telegram_bot"
memory_monitor_task_name = f"{TASK_NAME_PREFIX}memory_monitor"
stop_signal_task_name = f"{TASK_NAME_PREFIX}stop_signal_checker"

def dynamic_import_module(module_path: str):
    """动态导入模块，减少初始内存占用"""
    try:
        import importlib
        return importlib.import_module(module_path)
    except ImportError as e:
        logger.error(f"导入模块失败: {module_path}, 错误: {e}")
        return None

def format_task_results(task_results):
    # 使用列表解析格式化每个任务的结果
    formatted_results = []
    for task in task_results:
        # 去掉方括号以及方括号中的内容
        task_name = task['task_name']
        task_result = task['task_result']
        task_result_cleaned = re.sub(r'\[.*?\]', '', task_result).strip()  # 使用正则表达式去掉方括号内容
        formatted_results.append(f"[{task_name}] : {task_result_cleaned}")
    # 将格式化的结果用换行连接
    return "\n".join(formatted_results)

async def process_result(task):
    # 可以在这里看看所有活动任务
    # return {"task_name":current_file_name, "task_result":reason}
    task_ = task.result()  # 获取任务的返回结果，也就是这句“return {"task_name":current_file_name, "task_result":reason}”
    task_results.append(task_)
    active_tasks = asyncio.all_tasks()
    # 只过滤出我们自己创建的TASK_NAME_PREFIX开头的任务，并再去掉TASK_NAME_PREFIX方便msg显示
    my_bizz_active_tasks = [
        task.get_name().replace(TASK_NAME_PREFIX, '')
        for task in active_tasks
        if (task.get_name() and
            task.get_name() != telegram_bot_task_name and  # 精确排除telegram_bot任务
            task.get_name() != memory_monitor_task_name and  # 精确排除memory_monitor任务
            task.get_name() != stop_signal_task_name and  # 精确排除stop_signal_checker任务
            task.get_name().startswith(TASK_NAME_PREFIX))
    ]
    stopped_task = task_["task_name"]
    if not my_bizz_active_tasks:
        msg = f"随着 [{stopped_task}] 停止，没有任务在执行了"
        log_msg = f"{msg}"
        telegram_msg = f"{msg}"
    else:
        msg = f"虽然 [{stopped_task}] 停止，但还在执行: "
        log_msg = f"{msg}{my_bizz_active_tasks}"
        # 使用 "[]{}" 将任务名称连接为多行字符串
        task_list_formatted = "\n".join([f"[{task}]" for task in my_bizz_active_tasks])  # 将每项用方括号括起来
        telegram_msg = f"{msg}\n{task_list_formatted}"
    logger.info(log_msg)
    
    # send_email(f"量化程序状态通知", telegram_msg)
    # 发送Telegram消息
    await send_telegram_message_without_prefix(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, telegram_msg)

def task_done_callback(task):
    asyncio.ensure_future(process_result(task))  # 注册异步任务来处理结果

# 心跳检测，没必要了，因为随着业务任务多起来，telegram中的消息会比较频繁，只要有消息就证明程序还在正常执行中，如果定时有心跳提醒会比较多余
# 不如使用telegram机器人这种方式，当我想知道程序是否还在执行中时，只要在群组中发送相关命令即可
# async def heartbeat():
#     while True:
#         await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, "❤️ 心跳检测: 程序运行中")
#         await asyncio.sleep(3600)  # 每小时发送一次

async def main():
    # 显示启动横幅
    show_startup_banner(logger)

    # 确保只有一个实例在运行
    lock_file_name = get_app_config('app.process.lock_file', 'quant_program.lock')
    lock_file_path = os.path.join(os.path.dirname(__file__), lock_file_name)
    # 检查是否是重启场景（通过环境变量标识）
    is_restart = os.getenv("QUANT_RESTART") == "1"
    ensure_single_instance(lock_file_path, "量化程序", wait_for_release=is_restart)

    # 设置程序启动时间，用于过滤历史消息
    set_program_start_time()

    # 设置信号处理器
    setup_signal_handlers(logger)

    # 设置Telegram回复消息使用的logger
    set_telegram_logger(logger)

    # 从配置获取分隔符，如果没有配置则使用默认值
    divider_length = get_app_config('app.telegram.divider_length', 32)
    divider = "=" * divider_length

    logger.info(f"量化程序开始运行！")

    # 显示资源监控初始状态（轻量级）
    initial_memory = get_basic_memory_info()
    logger.info(f"📊 资源监控已启动 - 初始内存: {initial_memory['initial_memory_mb']} MB")



    # send_email(f"量化程序开始运行", "启动啦")
    # 发送Telegram消息（包含简化logo）
    simple_logo = get_simple_logo()
    await send_telegram_message_without_prefix(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, f"{simple_logo}量化程序开始运行！\n{divider}")

    # 初始化任务列表
    tasks = []

    try:
        # 启动机器人轮询任务（作为后台任务，目前暂用于探活）
        telegram_task = asyncio.create_task(run_telegram_bot(TELEGRAM_BOT_TOKEN, logger), name=telegram_bot_task_name)

        # 启动定期内存监控任务（作为后台任务）
        memory_monitor_task = asyncio.create_task(periodic_memory_monitor(logger), name=memory_monitor_task_name)

        # 启动停止信号等待任务（作为后台任务）
        stop_signal_task = asyncio.create_task(wait_for_stop_signal(logger), name=stop_signal_task_name)

        # 从配置文件获取要创建的任务配置
        enabled_monitors = get_app_config('app.tasks.enabled_monitors', [
            "monitor1_volume", "monitor2_pressure", "monitor3_divergence"
        ])
        enabled_traders = get_app_config('app.tasks.enabled_traders', [])

        # 构建完整的模块路径
        task_module_configs = []
        for monitor in enabled_monitors:
            task_module_configs.append(f"monitor.{monitor}")
        for trader in enabled_traders:
            task_module_configs.append(f"trader.{trader}")

        logger.info(f"📋 从配置加载的任务: 监控{len(enabled_monitors)}个, 交易{len(enabled_traders)}个")

        # 动态导入并创建任务配置
        task_configs = []
        for module_path in task_module_configs:
            module = dynamic_import_module(module_path)
            if module:
                task_configs.append((module.main, f"{TASK_NAME_PREFIX}{module.current_file_name}"))
                logger.info(f"✅ 成功导入模块: {module_path}")
                # 在每个模块导入后检查内存（关键节点监控，轻量级）
                get_basic_memory_info()
            else:
                logger.error(f"❌ 导入模块失败: {module_path}")

        # 将 main() 任务的名称设置为 TASK_NAME_PREFIX拼接上"main"
        # asyncio.current_task().set_name(f"{TASK_NAME_PREFIX}main")

        task_size = len(task_configs)
        task_names = [task_name.replace(TASK_NAME_PREFIX, "") for _, task_name in task_configs]
        logger.info(f"共包含 {task_size} 个任务: {task_names}")
        # 使用 "[]{}" 将任务名称连接为多行字符串
        task_names_formatted = "\n".join([f"[{task_name}]" for task_name in task_names])  # 将每项用方括号括起来
        # send_email(f"量化程序状态通知", f"共包含 {task_size} 个任务: {task_names}")
        # 发送Telegram消息
        await send_telegram_message_without_prefix(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, f"共包含 {task_size} 个任务: \n{task_names_formatted}")

        # 创建实际任务
        startup_delay = get_app_config('app.tasks.startup.delay_between_tasks', 0.5)
        tasks = []
        for task_func, task_name in task_configs:
            task = asyncio.create_task(task_func(), name=task_name)
            task.add_done_callback(task_done_callback)
            tasks.append(task)
            await asyncio.sleep(startup_delay)  # 使用配置的延迟时间

        # 关键节点监控：所有任务启动后检查内存（轻量级）
        memory_after_tasks = get_basic_memory_info()
        logger.info(f"📊 所有任务启动后内存: {memory_after_tasks['current_memory_mb']} MB (峰值: {memory_after_tasks['peak_memory_mb']} MB)")
            
        # 将 Telegram 任务、内存监控任务和停止信号检查任务也加入任务列表，与其他监控/交易任务一起通过 asyncio.gather 并发运行
        tasks.append(telegram_task)
        tasks.append(memory_monitor_task)
        tasks.append(stop_signal_task)
    
        # 执行所有异步任务并发运行，等待任何一个任务完成
        while True:
            done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)

            # 检查完成的任务中是否有停止信号
            stop_detected = False
            for task in done:
                try:
                    result = task.result()
                    if (isinstance(result, dict) and
                        result.get("task_name") == "stop_signal_checker"):
                        stop_reason = result.get("task_result", "未知停止原因")
                        logger.info(f"🛑 检测到停止信号：{stop_reason}，开始优雅退出")
                        stop_detected = True
                        break
                except Exception as e:
                    # 任务异常，记录但继续检查其他任务
                    logger.error(f"任务异常: {e}")

            if stop_detected:
                # 取消所有剩余任务
                for task in pending:
                    task.cancel()
                break

            # 如果没有检测到停止信号，但有任务完成了，可能是业务任务异常结束
            # 移除已完成的任务，继续等待其他任务
            tasks = list(pending)
            if not tasks:
                # 所有任务都完成了
                break
        
    except asyncio.CancelledError:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
    except Exception as e:
        logger.error(f"报错: {traceback.format_exc()}")
    finally:
        # 清理所有任务和资源
        for task in tasks:
            task.cancel()  # 请求取消所有任务

        # 等待所有任务完成，以确保所有的清理工作都完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # send_email(f"量化程序停止运行", f"原因: {reason}")
        # 发送Telegram消息
        # 获取最后消息延迟配置
        final_message_delay = get_app_config('app.telegram.final_message_delay', 0.5)
        await asyncio.sleep(final_message_delay)  # 这是最后一条消息，应该在Telegram中最后显示，所以等一会再发，确保是最后发送的
        # 格式化任务结果
        formatted_results = format_task_results(task_results)
        await send_telegram_message_without_prefix(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, f"量化程序停止运行！原因:\n{formatted_results}\n{divider}")
        
        # 共用同一个消息队列，等待消息队列清空，确保所有消息都被发送完毕
        await wait_for_message_queue(logger)

        # 关闭全局exchange实例
        try:
            logger.info("🔒 正在关闭Exchange连接...")
            await close_global_exchange()
            logger.info("🔒 Exchange已关闭")
            # 给一点时间确保连接完全关闭
            await asyncio.sleep(0.5)
        except Exception as e:
            logger.error(f"Exchange关闭失败: {e}")
            # 即使关闭失败，也要等待一下，避免资源泄露警告
            await asyncio.sleep(1)

        # 打印资源使用报告
        logger.info("📊 生成资源使用报告...")
        print_resource_report(logger)

        logger.info(f"量化程序停止运行！原因: {task_results}")

async def main_with_restart():
    """支持重启的主函数"""
    import sys
    import subprocess

    while True:
        try:
            # 运行主程序
            await main()

            # 检查是否需要重启
            signal_handler = get_signal_handler()
            if signal_handler.is_restarting:
                logger.info("🔄 检测到重启信号，正在重启程序...")

                # 确保所有资源都已正确关闭
                logger.info("🔄 等待资源清理完成...")
                await asyncio.sleep(2)  # 给足够时间让exchange连接关闭

                # 重启程序 - 保持原始启动方式
                python = sys.executable
                script = sys.argv[0]
                args = sys.argv[1:]

                # 检查是否有自定义的启动命令（通过环境变量）
                original_start_cmd = os.getenv('QUANT_START_CMD', '')

                if original_start_cmd:
                    # 使用原始启动命令，替换脚本路径
                    if 'caffeinate' in original_start_cmd:
                        restart_cmd = ['caffeinate', python, script] + args
                        logger.info(f"🔄 使用caffeinate重启: caffeinate {python} {script} {' '.join(args)}")
                    else:
                        restart_cmd = [python, script] + args
                        logger.info(f"🔄 重启命令: {python} {script} {' '.join(args)}")
                else:
                    # 默认启动方式
                    restart_cmd = [python, script] + args
                    logger.info(f"🔄 重启命令: {python} {script} {' '.join(args)}")

                # 获取重启延迟配置
                restart_delay = get_app_config('app.process.restart_delay', 3)

                # 使用延迟启动脚本来避免进程锁冲突
                restart_script = f"""
import time
import subprocess
import sys
import os

# 等待旧进程完全退出
time.sleep({restart_delay})

# 设置重启标识环境变量
env = os.environ.copy()
env['QUANT_RESTART'] = '1'

# 启动新进程
subprocess.Popen({restart_cmd}, env=env)
"""

                # 创建临时重启脚本
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                    f.write(restart_script)
                    restart_script_path = f.name

                # 启动延迟重启脚本
                subprocess.Popen([python, restart_script_path],
                               stdout=subprocess.DEVNULL,
                               stderr=subprocess.DEVNULL)

                logger.info("✅ 延迟重启脚本已启动，当前进程即将退出")
                break
            else:
                # 正常退出，不重启
                break

        except Exception as e:
            logger.error(f"程序运行异常: {e}")
            break

if __name__ == "__main__":
    # 运行支持重启的事件循环
    asyncio.run(main_with_restart())

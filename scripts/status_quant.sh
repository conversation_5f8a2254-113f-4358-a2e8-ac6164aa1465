#!/bin/bash

# 量化程序状态查询脚本
# 使用方法：
#   ./status_quant.sh         # 查看程序状态
#   ./status_quant.sh ps      # 查看相关进程
#   ./status_quant.sh logs    # 查看最新日志
#   ./status_quant.sh tail    # 实时查看日志

# 获取脚本所在目录，然后切换到项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

# 从配置文件读取锁文件名
get_lock_file_name() {
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from utils.config_manager import get_app_config
print(get_app_config('app.process.lock_file', 'quant_program.lock'))
" 2>/dev/null || echo "quant_program.lock"
}

# 锁文件路径
LOCK_FILE_NAME=$(get_lock_file_name)
LOCK_FILE="$PROJECT_ROOT/$LOCK_FILE_NAME"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查程序状态
check_status() {
    echo -e "${BLUE}📊 量化程序状态检查${NC}"
    echo "=================================="
    
    if [ -f "$LOCK_FILE" ]; then
        local pid=$(cat "$LOCK_FILE" 2>/dev/null)
        if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
            echo -e "${GREEN}✅ 程序状态: 正在运行${NC}"
            echo -e "${BLUE}📋 进程ID: $pid${NC}"
            
            # 获取进程信息
            if command -v ps >/dev/null 2>&1; then
                echo -e "${BLUE}📋 进程信息:${NC}"
                if [[ "$OSTYPE" == "darwin"* ]]; then
                    # macOS
                    ps -p "$pid" -o pid,ppid,etime,pcpu,pmem,command 2>/dev/null || echo "无法获取进程详细信息"
                else
                    # Linux
                    ps -p "$pid" -o pid,ppid,etime,pcpu,pmem,cmd 2>/dev/null || echo "无法获取进程详细信息"
                fi
            fi
            
            # 检查启动方式
            local start_method="普通Python"

            # 方法1: 检查环境变量
            if [ -n "$QUANT_START_CMD" ] && [ "$QUANT_START_CMD" = "caffeinate" ]; then
                start_method="caffeinate (防休眠)"
            else
                # 方法2: 检查进程树中是否有caffeinate
                if command -v ps >/dev/null 2>&1; then
                    # 获取完整的进程树信息
                    local process_tree=$(ps -ef | grep -E "(caffeinate.*python.*main\.py|python.*main\.py)" | grep -v grep)
                    if echo "$process_tree" | grep -q "caffeinate.*python.*main\.py"; then
                        start_method="caffeinate (防休眠)"
                    fi
                fi
            fi

            if [[ "$start_method" == *"caffeinate"* ]]; then
                echo -e "${BLUE}☕ 启动方式: $start_method${NC}"
            else
                echo -e "${BLUE}🐍 启动方式: $start_method${NC}"
            fi
            
        else
            echo -e "${RED}❌ 程序状态: 未运行 (锁文件存在但进程不存在)${NC}"
            echo -e "${YELLOW}🧹 清理过期锁文件...${NC}"
            rm -f "$LOCK_FILE"
        fi
    else
        echo -e "${RED}❌ 程序状态: 未运行${NC}"
    fi
    
    echo ""
    
    # 检查日志文件
    echo -e "${BLUE}📄 日志文件状态:${NC}"
    local log_files=(
        "logs/main.log"
        "logs/monitor1_volume.log"
        "logs/monitor2_pressure.log"
        "logs/monitor3_divergence.log"
        "logs/monitor4_oi.log"
        "logs/monitor5_ema_ma_crossover.log"
        "logs/monitor6_volume_oi_matrix.log"
        "logs/monitor7_bollinger_squeeze.log"
        "logs/trader1_ema.log"
        "logs/trader2_sentiment.log"
        "logs/trader3_orderbook.log"
    )
    
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            local size=$(du -h "$log_file" 2>/dev/null | cut -f1)
            # 兼容macOS和Linux的stat命令
            if [[ "$OSTYPE" == "darwin"* ]]; then
                local modified=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$log_file" 2>/dev/null)
            else
                local modified=$(stat -c "%y" "$log_file" 2>/dev/null | cut -d. -f1)
            fi
            echo -e "  ${GREEN}✅ $log_file${NC} (大小: $size, 修改: $modified)"
        else
            echo -e "  ${YELLOW}⚠️  $log_file (不存在)${NC}"
        fi
    done
}

# 查看最新日志
show_logs() {
    echo -e "${BLUE}📄 查看最新日志 (最后50行)${NC}"
    echo "=================================="
    
    local main_log="logs/main.log"
    if [ -f "$main_log" ]; then
        echo -e "${BLUE}📋 主程序日志:${NC}"
        tail -50 "$main_log"
    else
        echo -e "${RED}❌ 主程序日志文件不存在: $main_log${NC}"
    fi
}

# 实时查看日志
tail_logs() {
    echo -e "${BLUE}📄 实时查看日志 (按Ctrl+C退出)${NC}"
    echo "=================================="

    local main_log="logs/main.log"
    if [ -f "$main_log" ]; then
        echo -e "${BLUE}📋 实时跟踪主程序日志:${NC}"
        tail -f "$main_log"
    else
        echo -e "${RED}❌ 主程序日志文件不存在: $main_log${NC}"
    fi
}

# 查看相关进程
show_processes() {
    echo -e "${BLUE}🔍 查看量化程序相关进程${NC}"
    echo "=================================="

    # 使用精确的进程查找命令
    local processes=$(ps aux | grep python | grep -E "(main\.py|Quant)" | grep -v grep)

    if [ -n "$processes" ]; then
        echo -e "${GREEN}找到以下相关进程:${NC}"
        echo ""
        # 显示表头
        echo -e "${BLUE}USER       PID  %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND${NC}"
        echo "$processes"
    else
        echo -e "${YELLOW}⚠️  未找到相关的Python进程${NC}"
        echo ""
        echo -e "${BLUE}💡 提示: 如果程序正在运行但未显示，可能是因为:${NC}"
        echo -e "  • 进程名称不包含 'main.py' 或 'Quant'"
        echo -e "  • 程序使用了不同的启动方式"
        echo ""
        echo -e "${BLUE}🔍 您也可以手动执行以下命令查看所有Python进程:${NC}"
        echo -e "  ${BLUE}ps aux | grep python${NC}"
    fi

    echo ""
    echo -e "${BLUE}📋 完整命令: ${BLUE}ps aux | grep python | grep -E \"(main\.py|Quant)\" | grep -v grep${NC}"
}

# 显示帮助
show_help() {
    echo -e "${BLUE}量化程序状态查询脚本${NC}"
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo -e "  ${GREEN}./status_quant.sh${NC}         # 查看程序状态"
    echo -e "  ${GREEN}./status_quant.sh ps${NC}      # 查看相关进程"
    echo -e "  ${GREEN}./status_quant.sh logs${NC}    # 查看最新日志"
    echo -e "  ${GREEN}./status_quant.sh tail${NC}    # 实时查看日志"
    echo -e "  ${GREEN}./status_quant.sh help${NC}    # 显示帮助"
}

# 主逻辑
case "$1" in
    "ps")
        show_processes
        ;;
    "logs")
        show_logs
        ;;
    "tail")
        tail_logs
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    "")
        check_status
        ;;
    *)
        echo -e "${RED}❌ 未知参数: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac

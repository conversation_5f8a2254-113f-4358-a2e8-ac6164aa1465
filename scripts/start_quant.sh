#!/bin/bash

# 量化程序启动脚本
# 使用方法：
#   ./start_quant.sh          # 普通启动
#   ./start_quant.sh caffeinate # 使用caffeinate启动（防止Mac休眠）

# 获取脚本所在目录，然后切换到项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色定义
GREEN='\033[0;32m'
NC='\033[0m' # No Color

# 显示Logo（调用专门的logo脚本）
show_logo() {
    python3 "$PROJECT_ROOT/scripts/show_logo.py" 2>/dev/null || {
        # 如果Python调用失败，显示简化版本
        echo "🚀 量化交易程序启动"
        echo "=================================================="
    }
}

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python，请确保Python已安装并在PATH中"
    exit 1
fi

# 检查main.py是否存在
if [ ! -f "main.py" ]; then
    echo "❌ 错误: 未找到main.py文件"
    exit 1
fi

# 显示启动logo
show_logo
echo ""

# 设置启动命令环境变量
if [ "$1" = "caffeinate" ]; then
    # 检查是否在macOS上
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo -e "${GREEN}🚀 使用caffeinate启动量化程序（防止Mac休眠）...${NC}"
        export QUANT_START_CMD="caffeinate"
        exec caffeinate python main.py
    else
        echo -e "${GREEN}⚠️ 警告: caffeinate仅在macOS上可用，使用普通方式启动${NC}"
        export QUANT_START_CMD="python"
        exec python main.py
    fi
else
    echo -e "${GREEN}🚀 启动量化程序...${NC}"
    export QUANT_START_CMD="python"
    exec python main.py
fi

#!/usr/bin/env python3
"""
故障恢复工具：强制清理和重启量化程序

⚠️  这是一个故障恢复工具，用于处理以下情况：
- 正常停止脚本无法停止程序
- 程序异常退出留下僵尸进程
- 需要完全重置运行环境
- 日志文件过大需要清理

🎯 日常使用请优先使用：./quant.sh start/stop/restart

使用方法：
    python tools/cleanup_and_restart.py          # 交互式清理
    python tools/cleanup_and_restart.py --restart # 清理后自动重启
    chmod +x tools/cleanup_and_restart.py && ./tools/cleanup_and_restart.py
"""

import os
import sys
import signal
import time
import subprocess
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from utils.config_manager import get_app_config

def detect_original_start_method():
    """检测原始启动方式"""
    # 检查环境变量
    start_cmd = os.environ.get('QUANT_START_CMD', '')
    if start_cmd == 'caffeinate':
        return 'caffeinate'
    elif start_cmd == 'python':
        return 'python'

    # 检查进程命令行
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        for line in result.stdout.split('\n'):
            if 'main.py' in line and 'python' in line:
                if 'caffeinate' in line:
                    return 'caffeinate'
                else:
                    return 'python'
    except:
        pass

    # 默认返回python
    return 'python'

def restart_program_with_correct_method():
    """使用正确的方式重启程序"""
    project_root = Path(__file__).parent.parent
    start_method = detect_original_start_method()

    print(f"🔍 检测到原始启动方式: {start_method}")

    # 优先使用脚本系统重启，这样能保持一致性
    start_script = project_root / "scripts" / "start_quant.sh"
    quant_script = project_root / "quant.sh"

    if quant_script.exists():
        print("🚀 使用脚本系统重启（推荐方式）...")
        if start_method == 'caffeinate':
            subprocess.Popen(['./quant.sh', 'start', 'caffeinate'], cwd=project_root)
        else:
            subprocess.Popen(['./quant.sh', 'start'], cwd=project_root)
    elif start_script.exists():
        print("🚀 使用启动脚本重启...")
        if start_method == 'caffeinate':
            subprocess.Popen(['./scripts/start_quant.sh', 'caffeinate'], cwd=project_root)
        else:
            subprocess.Popen(['./scripts/start_quant.sh'], cwd=project_root)
    else:
        # 回退到直接启动
        print("⚠️  未找到启动脚本，使用直接启动方式...")
        if start_method == 'caffeinate' and sys.platform == 'darwin':
            print("🚀 使用caffeinate方式重启（防止Mac休眠）...")
            env = os.environ.copy()
            env['QUANT_START_CMD'] = 'caffeinate'
            subprocess.Popen(['caffeinate', sys.executable, 'main.py'],
                           cwd=project_root, env=env)
        else:
            print("🚀 使用普通方式重启...")
            env = os.environ.copy()
            env['QUANT_START_CMD'] = 'python'
            subprocess.Popen([sys.executable, 'main.py'],
                           cwd=project_root, env=env)

def find_and_kill_processes():
    """查找并终止所有相关的Python进程"""
    print("正在查找量化程序相关进程...")
    
    # 获取项目根目录的绝对路径
    current_dir = str(Path(__file__).parent.parent.absolute())
    print(f"项目目录: {current_dir}")
    
    try:
        # 使用ps命令查找相关进程
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        killed_processes = []
        found_processes = []
        
        for line in lines:
            # 精确匹配：必须是Python进程且在当前项目目录下运行
            if ('python' in line and current_dir in line and
                'cleanup_and_restart.py' not in line):
                
                parts = line.split()
                if len(parts) >= 2:
                    try:
                        pid = int(parts[1])
                        command = ' '.join(parts[10:])
                        
                        # 进一步检查是否是量化程序相关文件
                        if (any(keyword in command for keyword in [
                            'main.py', 'monitor', 'trader', '/monitor/', '/trader/',
                            'quant'
                        ])):
                            found_processes.append({
                                'pid': pid,
                                'command': command
                            })
                        
                    except (ValueError, ProcessLookupError, PermissionError) as e:
                        print(f"处理进程信息时出错: {e}")
        
        if not found_processes:
            print("未发现量化程序相关进程")
            return
        
        print(f"\n发现 {len(found_processes)} 个量化程序进程:")
        for i, proc in enumerate(found_processes, 1):
            print(f"{i}. PID {proc['pid']}: {proc['command']}")
        
        print("\n选择操作:")
        print("1. 终止所有进程")
        print("2. 选择性终止")
        print("3. 取消")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == '1':
            # 终止所有进程
            for proc in found_processes:
                try:
                    os.kill(proc['pid'], signal.SIGTERM)
                    killed_processes.append(proc['pid'])
                    print(f"已发送终止信号给进程 {proc['pid']}")
                except (ProcessLookupError, PermissionError) as e:
                    print(f"无法终止进程 {proc['pid']}: {e}")
                    
        elif choice == '2':
            # 选择性终止
            for proc in found_processes:
                confirm = input(f"是否终止进程 {proc['pid']}？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    try:
                        os.kill(proc['pid'], signal.SIGTERM)
                        killed_processes.append(proc['pid'])
                        print(f"已发送终止信号给进程 {proc['pid']}")
                    except (ProcessLookupError, PermissionError) as e:
                        print(f"无法终止进程 {proc['pid']}: {e}")
        else:
            print("取消进程终止操作")
            return
        
        if killed_processes:
            # 从配置读取等待时间
            try:
                wait_time = get_app_config('app.cleanup.process_kill_wait', 5)
            except:
                wait_time = 5
            print(f"已发送终止信号给 {len(killed_processes)} 个进程，等待{wait_time}秒...")
            time.sleep(wait_time)
            
            # 检查是否还有进程在运行，如果有则强制终止
            for pid in killed_processes:
                try:
                    os.kill(pid, 0)  # 检查进程是否还存在
                    print(f"强制终止进程 {pid}")
                    os.kill(pid, signal.SIGKILL)
                except ProcessLookupError:
                    pass  # 进程已经终止
                except PermissionError as e:
                    print(f"无权限终止进程 {pid}: {e}")
        else:
            print("未发现相关进程")
            
    except Exception as e:
        print(f"查找进程时出错: {e}")

def cleanup_lock_files():
    """清理锁文件"""
    print("正在清理锁文件...")

    # 锁文件在项目根目录
    project_root = Path(__file__).parent.parent
    lock_files = list(project_root.glob("*.lock"))
    
    for lock_file in lock_files:
        try:
            lock_file.unlink()
            print(f"已删除锁文件: {lock_file}")
        except Exception as e:
            print(f"删除锁文件 {lock_file} 失败: {e}")

def cleanup_large_log_files(max_size_mb=None):
    """清理过大的日志文件"""
    if max_size_mb is None:
        # 从配置读取最大文件大小
        try:
            max_size_mb = get_app_config('app.cleanup.max_log_size_mb', 50)
        except:
            max_size_mb = 50
    print(f"正在清理超过 {max_size_mb}MB 的日志文件...")

    # 日志文件在logs目录
    project_root = Path(__file__).parent.parent
    logs_dir = project_root / "logs"
    log_files = list(logs_dir.glob("*.log")) if logs_dir.exists() else []
    
    for log_file in log_files:
        try:
            size_mb = log_file.stat().st_size / (1024 * 1024)
            if size_mb > max_size_mb:
                # 备份最后1000行（跨平台兼容）
                backup_file = log_file.with_suffix('.log.backup')
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()

                    # 保留最后1000行
                    backup_lines = lines[-1000:] if len(lines) > 1000 else lines

                    with open(backup_file, 'w', encoding='utf-8') as f:
                        f.writelines(backup_lines)

                    # 清空原文件
                    log_file.write_text("", encoding='utf-8')
                    print(f"已清理日志文件: {log_file} ({size_mb:.1f}MB) -> 备份到 {backup_file}")
                except Exception as backup_error:
                    print(f"备份日志文件失败: {backup_error}，直接清空文件")
                    log_file.write_text("", encoding='utf-8')
                    print(f"已清理日志文件: {log_file} ({size_mb:.1f}MB)")
        except Exception as e:
            print(f"处理日志文件 {log_file} 失败: {e}")

def main():
    print("=" * 60)
    print("🚨 量化程序故障恢复工具")
    print("=" * 60)
    print("⚠️  这是一个故障恢复工具，用于强制清理环境")
    print("💡 日常使用请优先使用: ./quant.sh start/stop/restart")
    print()
    print("📋 此工具将执行以下操作:")
    print("   1. 🔍 扫描并停止所有相关Python进程")
    print("   2. 🧹 清理所有锁文件")
    # 从配置读取日志文件大小限制用于显示
    try:
        max_log_size = get_app_config('app.cleanup.max_log_size_mb', 50)
    except:
        max_log_size = 50
    print(f"   3. 📁 清理大日志文件(>{max_log_size}MB)")
    print("   4. 🔄 可选择重启程序")
    print()
    print("🎯 适用场景:")
    print("   - 正常停止脚本无法停止程序")
    print("   - 程序异常退出留下僵尸进程")
    print("   - 需要完全重置运行环境")

    # 先检查是否真的需要使用此工具
    print("\n🔍 检查程序状态...")
    project_root = Path(__file__).parent.parent

    # 从配置文件读取锁文件名
    try:
        lock_file_name = get_app_config('app.process.lock_file', 'quant_program.lock')
    except:
        lock_file_name = 'quant_program.lock'  # 回退到默认值

    lock_file = project_root / lock_file_name

    has_lock_file = lock_file.exists()
    has_running_process = False

    # 检查锁文件
    if has_lock_file:
        print("📋 发现锁文件存在")
        try:
            with open(lock_file, 'r') as f:
                pid = f.read().strip()
            print(f"   锁文件PID: {pid}")
        except:
            print("   ⚠️  锁文件内容无法读取")
    else:
        print("📋 未发现锁文件")

    # 检查是否有相关进程
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        running_processes = []
        for line in result.stdout.split('\n'):
            if 'main.py' in line and 'python' in line and 'cleanup_and_restart' not in line:
                running_processes.append(line.strip())
                has_running_process = True

        if running_processes:
            print("📋 发现量化程序进程:")
            for proc in running_processes[:3]:  # 最多显示3个
                print(f"   {proc}")
        else:
            print("📋 未发现相关进程")
    except:
        print("⚠️  无法检查进程状态")

    # 状态总结
    print(f"\n📊 状态总结:")
    print(f"   锁文件: {'存在' if has_lock_file else '不存在'}")
    print(f"   运行进程: {'存在' if has_running_process else '不存在'}")

    # 根据检查结果给出建议
    if has_lock_file and has_running_process:
        print("\n✅ 程序正常运行中")
        print("💡 如果程序运行正常，无需使用故障恢复工具")
        suggest_normal = input("   程序正在正常运行，是否仍要强制清理？(y/N): ").strip().lower()
        if suggest_normal not in ['y', 'yes']:
            print("\n💡 建议使用正常脚本:")
            print("   ./quant.sh status   # 检查详细状态")
            print("   ./quant.sh stop     # 正常停止程序")
            print("   ./quant.sh restart  # 正常重启程序")
            return
        else:
            print("\n⚠️  您选择强制清理正在运行的程序")
    elif not has_lock_file and not has_running_process:
        print("\n✅ 程序未运行，环境干净")
        suggest_normal = input("   环境状态正常，是否仍要继续清理？(y/N): ").strip().lower()
        if suggest_normal not in ['y', 'yes']:
            print("\n💡 建议使用正常脚本:")
            print("   ./quant.sh start    # 启动程序")
            print("   ./quant.sh status   # 检查状态")
            return
    elif has_lock_file and not has_running_process:
        print("\n⚠️  发现僵尸锁文件（程序已退出但锁文件未清理）")
        print("💡 这种情况适合使用故障恢复工具")
    elif not has_lock_file and has_running_process:
        print("\n⚠️  发现孤儿进程（程序在运行但锁文件丢失）")
        print("💡 这种情况适合使用故障恢复工具")

    confirm = input("\n⚠️  确认执行故障恢复？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 操作已取消")
        return
    
    # 1. 终止所有相关进程
    find_and_kill_processes()
    
    # 2. 清理锁文件
    cleanup_lock_files()
    
    # 3. 清理大日志文件
    cleanup_large_log_files()
    
    print("\n清理完成！")
    
    # 询问是否重启
    if len(sys.argv) > 1 and sys.argv[1] == '--restart':
        print("正在重启量化程序...")
        # 从配置读取重启延迟
        try:
            restart_delay = get_app_config('app.process.restart_delay', 2)
        except:
            restart_delay = 2
        time.sleep(restart_delay)
        restart_program_with_correct_method()
        print("✅ 量化程序已重启")
    else:
        restart = input("\n是否重启量化程序？(y/N): ").strip().lower()
        if restart in ['y', 'yes']:
            print("正在重启量化程序...")
            # 从配置读取重启延迟
            try:
                restart_delay = get_app_config('app.process.restart_delay', 2)
            except:
                restart_delay = 2
            time.sleep(restart_delay)
            restart_program_with_correct_method()
            print("✅ 量化程序已重启")

if __name__ == "__main__":
    main()
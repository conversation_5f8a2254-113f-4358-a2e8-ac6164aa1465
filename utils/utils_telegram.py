import asyncio
import time
from collections import defaultdict
from telegram import Bo<PERSON>, ReplyKeyboardMarkup, KeyboardButton, ReplyKeyboardRemove
from constants import TASK_DIVIDER
from telegram import Update
from telegram.ext import Application<PERSON><PERSON>er, MessageHandler, filters, ContextTypes
from .config_manager import get_telegram_config
from .signal_handler import trigger_stop, trigger_restart

# 全局限流控制
_message_queue = asyncio.Queue()
_last_send_time = defaultdict(float)
_rate_limit_lock = asyncio.Lock()
_queue_processor_started = False
_program_start_time = None  # 记录程序启动时间，用于过滤历史消息



# 全局logger，用于reply_to_message函数
_global_logger = None

async def _process_message_queue(logger=None):
    """处理消息队列，确保不超过Telegram API限制"""
    # 使用传入的logger，如果没有则使用print
    log_error = logger.error if logger else print

    last_batch_info = {}  # 记录每个聊天的最后一批消息信息

    while True:
        try:
            # 从队列中获取消息
            message_data = await _message_queue.get()

            # Telegram API限制：每秒最多30条消息，每分钟最多20条消息给同一个聊天
            current_time = time.time()
            chat_key = f"{message_data['chat_id']}"

            # 检查是否需要等待（每个聊天每0.3秒最多1条消息）
            time_since_last = current_time - _last_send_time[chat_key]
            if time_since_last < 0.3:
                wait_time = 0.3 - time_since_last
                await asyncio.sleep(wait_time)

            # 发送消息
            try:
                bot = Bot(token=message_data['bot_token'])
                await bot.send_message(
                    chat_id=message_data['chat_id'],
                    text=message_data['text'],
                    disable_web_page_preview=True
                )
                _last_send_time[chat_key] = time.time()

                # 记录这个聊天的最后一条消息信息
                last_batch_info[chat_key] = {
                    'bot_token': message_data['bot_token'],
                    'chat_id': message_data['chat_id'],
                    'last_message_time': time.time()
                }

            except Exception as e:
                # 如果是限流错误，等待更长时间后重试
                if "Flood control exceeded" in str(e):
                    print(f"Telegram限流，等待30秒后重试...")
                    await asyncio.sleep(30)
                    # 重新放入队列
                    await _message_queue.put(message_data)
                    # 标记当前任务完成（因为我们重新放入了队列）
                    _message_queue.task_done()
                else:
                    print(f"发送Telegram消息失败: {e}")
                    # 标记任务完成
                    _message_queue.task_done()
            else:
                # 成功发送，标记任务完成
                _message_queue.task_done()

                # 检查队列是否为空，如果为空则发送分割线
                await asyncio.sleep(1)  # 短暂等待，确保没有新消息立即加入
                if _message_queue.empty() and chat_key in last_batch_info:
                    # 发送分割线
                    try:
                        await bot.send_message(
                            chat_id=last_batch_info[chat_key]['chat_id'],
                            text=TASK_DIVIDER,
                            disable_web_page_preview=True
                        )
                        # 更新发送时间
                        _last_send_time[chat_key] = time.time()
                    except Exception as e:
                        log_error(f"发送分割线失败: {e}")

        except Exception as e:
            log_error(f"处理消息队列时出错: {e}")
            await asyncio.sleep(1)

async def _ensure_queue_processor(logger=None):
    """确保消息队列处理器已启动"""
    global _queue_processor_started
    async with _rate_limit_lock:
        if not _queue_processor_started:
            asyncio.create_task(_process_message_queue(logger))
            _queue_processor_started = True

async def send_telegram_message(bot_token, chat_id, prefix, message, logger=None):
    """
    发送 Telegram 消息（带限流）
    :param bot_token: Bot 的 API Token
    :param chat_id: 接收消息的 Chat ID
    :param prefix: 消息前缀
    :param message: 要发送的消息内容
    :param logger: 可选的logger实例
    """
    await _ensure_queue_processor(logger)
    
    message_data = {
        'bot_token': bot_token,
        'chat_id': chat_id,
        'text': f"[{prefix}] {message}"
    }
    
    await _message_queue.put(message_data)

async def send_telegram_message_without_prefix(bot_token, chat_id, message, logger=None):
    """
    发送 Telegram 消息（带限流）
    :param bot_token: Bot 的 API Token
    :param chat_id: 接收消息的 Chat ID
    :param message: 要发送的消息内容
    :param logger: 可选的logger实例
    """
    await _ensure_queue_processor(logger)
    
    message_data = {
        'bot_token': bot_token,
        'chat_id': chat_id,
        'text': message
    }
    
    await _message_queue.put(message_data)



def set_program_start_time():
    """设置程序启动时间"""
    global _program_start_time
    _program_start_time = time.time()



def set_telegram_logger(logger):
    """设置Telegram回复消息使用的logger"""
    global _global_logger
    _global_logger = logger

def create_command_keyboard():
    """创建命令快捷键盘"""
    keyboard = [
        [KeyboardButton("状态"), KeyboardButton("重启")],
        [KeyboardButton("停止"), KeyboardButton("重载配置")],
        [KeyboardButton("隐藏键盘")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

async def reply_to_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """回复消息，只回复程序运行期间的消息，并处理停止命令"""
    # 使用全局logger，如果没有则使用print
    log_info = _global_logger.info if _global_logger else print
    log_warning = _global_logger.warning if _global_logger else print

    if update.message and update.message.chat.type in ["group", "supergroup"]:
        # 检查消息时间是否在程序启动之后
        message_time = update.message.date.timestamp()
        if _program_start_time is None or message_time >= _program_start_time:
            message_text = update.message.text.strip()
            # 处理群组中的@bot命令，移除@bot_name部分
            if '@' in message_text and message_text.startswith('/'):
                message_text = message_text.split('@')[0]
            user_info = update.message.from_user.username or update.message.from_user.first_name

            # 检查是否是停止命令
            stop_commands = get_telegram_config('telegram.commands.stop', [])
            if message_text.lower() in [cmd.lower() for cmd in stop_commands]:
                stop_reply = get_telegram_config('telegram.messages.stop_reply', '🛑 收到停止指令，量化程序即将关闭...')
                await update.message.reply_text(stop_reply)
                log_info(f"📱 收到停止指令: '{message_text}' - 发送者: {user_info}")
                # 使用新的信号处理器触发停止
                trigger_stop("远程停止指令", _global_logger)
                log_info(f"📱 Telegram远程停止指令已触发 - 发送者: {user_info}")
                return

            # 检查是否是状态查询命令
            status_commands = get_telegram_config('telegram.commands.status', [])
            if message_text.lower() in [cmd.lower() for cmd in status_commands]:
                status_reply = get_telegram_config('telegram.messages.status_reply', '✅ 量化程序运行正常，所有任务正在执行中')
                # 如果是第一次查询状态，显示快捷键盘
                if message_text.lower() in ['/status', '/info', '状态']:
                    await update.message.reply_text(status_reply, reply_markup=create_command_keyboard())
                else:
                    await update.message.reply_text(status_reply)
                log_info(f"📱 收到状态查询 - 发送者: {user_info}")
                return

            # 检查是否是重启命令
            restart_commands = get_telegram_config('telegram.commands.restart', [])
            if message_text.lower() in [cmd.lower() for cmd in restart_commands]:
                restart_reply = get_telegram_config('telegram.messages.restart_reply', '🔄 收到重启指令，量化程序即将重启...')
                await update.message.reply_text(restart_reply)
                log_info(f"📱 收到重启指令: '{message_text}' - 发送者: {user_info}")
                # 使用新的信号处理器触发重启
                trigger_restart("远程重启指令", _global_logger)
                log_info(f"📱 Telegram远程重启指令已触发 - 发送者: {user_info}")
                return

            # 检查是否是重载配置命令
            reload_commands = get_telegram_config('telegram.commands.reload', [])
            if message_text.lower() in [cmd.lower() for cmd in reload_commands]:
                reload_reply = get_telegram_config('telegram.messages.reload_reply', '🔄 收到重载指令，正在重新加载所有配置...')
                await update.message.reply_text(reload_reply)
                log_info(f"📱 收到重载配置指令: '{message_text}' - 发送者: {user_info}")

                # 重载所有配置
                from utils.config_manager import config_manager
                config_manager.reload_all_configs()

                success_reply = get_telegram_config('telegram.messages.reload_success_reply', '✅ 配置重载完成')
                await update.message.reply_text(success_reply)
                log_info(f"📱 配置重载完成 - 发送者: {user_info}")
                return

            # 检查是否是重载监控配置命令
            reload_monitor_commands = get_telegram_config('telegram.commands.reload_monitor', [])
            if message_text.lower() in [cmd.lower() for cmd in reload_monitor_commands]:
                reload_reply = get_telegram_config('telegram.messages.reload_monitor_reply', '🔄 收到重载指令，正在重新加载监控配置...')
                await update.message.reply_text(reload_reply)
                log_info(f"📱 收到重载监控配置指令: '{message_text}' - 发送者: {user_info}")

                # 重载监控配置
                from utils.config_manager import config_manager
                config_manager.reload_config('monitor')

                success_reply = get_telegram_config('telegram.messages.reload_success_reply', '✅ 配置重载完成')
                await update.message.reply_text(success_reply)
                log_info(f"📱 监控配置重载完成 - 发送者: {user_info}")
                return

            # 检查是否是重载应用配置命令
            reload_app_commands = get_telegram_config('telegram.commands.reload_app', [])
            if message_text.lower() in [cmd.lower() for cmd in reload_app_commands]:
                reload_reply = get_telegram_config('telegram.messages.reload_app_reply', '🔄 收到重载指令，正在重新加载应用配置...')
                await update.message.reply_text(reload_reply)
                log_info(f"📱 收到重载应用配置指令: '{message_text}' - 发送者: {user_info}")

                # 重载应用配置
                from utils.config_manager import config_manager
                config_manager.reload_config('app')

                success_reply = get_telegram_config('telegram.messages.reload_success_reply', '✅ 配置重载完成')
                await update.message.reply_text(success_reply)
                log_info(f"📱 应用配置重载完成 - 发送者: {user_info}")
                return

            # 检查是否是重载交易配置命令
            reload_trader_commands = get_telegram_config('telegram.commands.reload_trader', [])
            if message_text.lower() in [cmd.lower() for cmd in reload_trader_commands]:
                reload_reply = get_telegram_config('telegram.messages.reload_trader_reply', '🔄 收到重载指令，正在重新加载交易配置...')
                await update.message.reply_text(reload_reply)
                log_info(f"📱 收到重载交易配置指令: '{message_text}' - 发送者: {user_info}")

                # 重载交易配置
                from utils.config_manager import config_manager
                config_manager.reload_config('trader')

                success_reply = get_telegram_config('telegram.messages.reload_success_reply', '✅ 配置重载完成')
                await update.message.reply_text(success_reply)
                log_info(f"📱 交易配置重载完成 - 发送者: {user_info}")
                return

            # 检查是否是重载交易所配置命令
            reload_exchange_commands = get_telegram_config('telegram.commands.reload_exchange', [])
            if message_text.lower() in [cmd.lower() for cmd in reload_exchange_commands]:
                reload_reply = get_telegram_config('telegram.messages.reload_exchange_reply', '🔄 收到重载指令，正在重新加载交易所配置...')
                await update.message.reply_text(reload_reply)
                log_info(f"📱 收到重载交易所配置指令: '{message_text}' - 发送者: {user_info}")

                # 重载交易所配置
                from utils.config_manager import config_manager
                config_manager.reload_config('exchange')

                success_reply = get_telegram_config('telegram.messages.reload_success_reply', '✅ 配置重载完成')
                await update.message.reply_text(success_reply)
                log_info(f"📱 交易所配置重载完成 - 发送者: {user_info}")
                return

            # 检查是否是隐藏键盘命令
            if message_text.lower() in ['隐藏键盘', 'hide', 'close']:
                await update.message.reply_text("键盘已隐藏", reply_markup=ReplyKeyboardRemove())
                log_info(f"📱 隐藏键盘 - 发送者: {user_info}")
                return

            # 其他消息不回复，只记录日志
            log_info(f"📱 收到未识别消息: '{message_text}' - 发送者: {user_info}")
        # 如果是历史消息，不回复

def init_telegram_bot(bot_token: str):
    """初始化机器人（不启动轮询）"""
    return ApplicationBuilder().token(bot_token).pool_timeout(10).build()

async def wait_for_message_queue(logger=None):
    """等待消息队列中的所有消息都被发送完毕

    Args:
        logger: 日志记录器，如果提供则使用logger记录，否则使用print
    """
    # 使用传入的logger，如果没有则使用print
    log_info = logger.info if logger else print
    log_warning = logger.warning if logger else print

    queue_size = _message_queue.qsize()
    if queue_size > 0:
        # 动态计算超时时间：基础时间 + 每条消息的预估时间
        base_timeout = 10  # 基础等待时间（秒）
        per_message_timeout = 2  # 每条消息预估处理时间（秒）
        max_timeout = 300  # 最大超时时间（5分钟）

        dynamic_timeout = min(base_timeout + (queue_size * per_message_timeout), max_timeout)

        log_info(f"📤 等待消息队列清空，当前队列大小: {queue_size}，动态超时: {dynamic_timeout}秒")

        try:
            await asyncio.wait_for(_message_queue.join(), timeout=dynamic_timeout)
            log_info("✅ 消息队列已清空")
        except asyncio.TimeoutError:
            remaining_size = _message_queue.qsize()
            sent_count = queue_size - remaining_size
            log_warning(f"⚠️ 消息队列清空超时，已发送: {sent_count}/{queue_size}，剩余: {remaining_size}条")
            log_info("💡 建议：如果经常超时，可以增加per_message_timeout值")
    else:
        log_info("📭 消息队列已为空，无需等待")

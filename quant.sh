#!/bin/bash

# 量化程序管理脚本 - 一站式管理工具
# 使用方法：
#   ./quant.sh start [caffeinate]  # 启动程序
#   ./quant.sh stop [force]        # 停止程序
#   ./quant.sh restart [caffeinate] # 重启程序
#   ./quant.sh status              # 查看状态
#   ./quant.sh logs                # 查看日志
#   ./quant.sh tail                # 实时日志

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示Logo（调用专门的logo脚本）
show_logo() {
    python3 "$PROJECT_ROOT/scripts/show_logo.py" 2>/dev/null || {
        # 如果Python调用失败，显示简化版本
        echo -e "${CYAN}"
        echo "  ██████  ██    ██  █████  ███    ██ ████████ "
        echo " ██    ██ ██    ██ ██   ██ ████   ██    ██    "
        echo " ██    ██ ██    ██ ███████ ██ ██  ██    ██    "
        echo " ██ ▄▄ ██ ██    ██ ██   ██ ██  ██ ██    ██    "
        echo "  ██████   ██████  ██   ██ ██   ████    ██    "
        echo "     ▀▀                                       "
        echo -e "${NC}"
        echo -e "${BLUE}        量化交易程序管理工具 v1.0${NC}"
        echo "=================================================="
    }
}

# 显示帮助
show_help() {
    show_logo
    echo -e "${YELLOW}使用方法:${NC}"
    echo ""
    echo -e "${GREEN}启动相关:${NC}"
    echo -e "  ${CYAN}./quant.sh start${NC}              # 普通启动"
    echo -e "  ${CYAN}./quant.sh start caffeinate${NC}   # 使用caffeinate启动(防休眠)"
    echo ""
    echo -e "${GREEN}停止相关:${NC}"
    echo -e "  ${CYAN}./quant.sh stop${NC}               # 优雅停止"
    echo -e "  ${CYAN}./quant.sh stop force${NC}         # 强制停止"
    echo -e "  ${CYAN}./quant.sh stop telegram${NC}      # 通过Telegram停止"
    echo ""
    echo -e "${GREEN}重启相关:${NC}"
    echo -e "  ${CYAN}./quant.sh restart${NC}            # 重启程序"
    echo -e "  ${CYAN}./quant.sh restart caffeinate${NC} # 使用caffeinate重启"
    echo ""
    echo -e "${GREEN}状态查询:${NC}"
    echo -e "  ${CYAN}./quant.sh status${NC}             # 查看程序状态"
    echo -e "  ${CYAN}./quant.sh ps${NC}                 # 查看相关进程"
    echo -e "  ${CYAN}./quant.sh logs${NC}               # 查看最新日志"
    echo -e "  ${CYAN}./quant.sh tail${NC}               # 实时查看日志"
    echo ""
    echo -e "${GREEN}其他:${NC}"
    echo -e "  ${CYAN}./quant.sh help${NC}               # 显示帮助"
    echo ""
    echo -e "${YELLOW}Telegram远程控制:${NC}"
    echo -e "  发送 ${CYAN}/start${NC} 或 ${CYAN}启动程序${NC} - 启动程序"
    echo -e "  发送 ${CYAN}/stop${NC} 或 ${CYAN}停止程序${NC} - 停止程序"
    echo -e "  发送 ${CYAN}/restart${NC} 或 ${CYAN}重启程序${NC} - 重启程序"
    echo -e "  发送 ${CYAN}/status${NC} 或 ${CYAN}状态${NC} - 查询状态"
}

# 启动程序
start_program() {
    echo -e "${BLUE}🚀 启动量化程序...${NC}"

    if [ "$1" = "caffeinate" ]; then
        ./scripts/start_quant.sh caffeinate
    else
        ./scripts/start_quant.sh
    fi
}

# 停止程序
stop_program() {
    echo -e "${BLUE}🛑 停止量化程序...${NC}"

    case "$1" in
        "force")
            ./scripts/stop_quant.sh force
            ;;
        "telegram")
            ./scripts/stop_quant.sh telegram
            ;;
        *)
            ./scripts/stop_quant.sh
            ;;
    esac
}

# 重启程序
restart_program() {
    echo -e "${BLUE}🔄 重启量化程序...${NC}"

    # 先停止
    ./scripts/stop_quant.sh

    if [ $? -eq 0 ]; then
        echo -e "${BLUE}⏳ 等待3秒后重新启动...${NC}"
        sleep 3

        # 再启动
        if [ "$1" = "caffeinate" ]; then
            ./scripts/start_quant.sh caffeinate
        else
            ./scripts/start_quant.sh
        fi
    else
        echo -e "${RED}❌ 停止程序失败，取消重启${NC}"
    fi
}

# 查看状态
show_status() {
    ./scripts/status_quant.sh
}

# 查看日志
show_logs() {
    ./scripts/status_quant.sh logs
}

# 实时日志
tail_logs() {
    ./scripts/status_quant.sh tail
}

# 查看相关进程
show_processes() {
    echo -e "${BLUE}🔍 查看量化程序相关进程${NC}"
    echo "=================================="

    # 使用您提供的精确命令
    local processes=$(ps aux | grep python | grep -E "(main\.py|Quant)" | grep -v grep)

    if [ -n "$processes" ]; then
        echo -e "${GREEN}找到以下相关进程:${NC}"
        echo ""
        # 显示表头
        echo -e "${CYAN}USER       PID  %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND${NC}"
        echo "$processes"
    else
        echo -e "${YELLOW}⚠️  未找到相关的Python进程${NC}"
        echo ""
        echo -e "${BLUE}💡 提示: 如果程序正在运行但未显示，可能是因为:${NC}"
        echo -e "  • 进程名称不包含 'main.py' 或 'Quant'"
        echo -e "  • 程序使用了不同的启动方式"
        echo ""
        echo -e "${BLUE}🔍 您也可以手动执行以下命令查看所有Python进程:${NC}"
        echo -e "  ${CYAN}ps aux | grep python${NC}"
    fi

    echo ""
    echo -e "${BLUE}📋 完整命令: ${CYAN}ps aux | grep python | grep -E \"(main\.py|Quant)\" | grep -v grep${NC}"
}

# 主逻辑
case "$1" in
    "start")
        start_program "$2"
        ;;
    "stop")
        stop_program "$2"
        ;;
    "restart")
        restart_program "$2"
        ;;
    "status")
        show_status
        ;;
    "ps")
        show_processes
        ;;
    "logs")
        show_logs
        ;;
    "tail")
        tail_logs
        ;;
    "help"|"-h"|"--help"|"")
        show_help
        ;;
    *)
        echo -e "${RED}❌ 未知命令: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
